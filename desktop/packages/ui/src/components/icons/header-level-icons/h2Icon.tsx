import type React from 'react';

interface H2IconProps {
    className?: string;
    letterClassName?: string;
    levelClassName?: string;
    [key: string]: any;
}

const H2Icon: React.FC<H2IconProps> = ({
    className,
    letterClassName,
    levelClassName,
    ...props
}) => (
    <svg
        width="15"
        height="15"
        viewBox="0 0 15 15"
        xmlns="http://www.w3.org/2000/svg"
        className={className}
        {...props}
    >
        <path
            className={levelClassName}
            d="M9.05 7.29992C9.05 7.54845 9.25147 7.74992 9.5 7.74992C9.74853 7.74992 9.95 7.54845 9.95 7.29992H9.05ZM10.8093 10.4177L11.1278 10.7356L10.8093 10.4177ZM9 12.0266C8.75147 12.0266 8.55 12.2281 8.55 12.4766C8.55 12.7252 8.75147 12.9266 9 12.9266V12.0266ZM12.6504 12.9266C12.8989 12.9266 13.1004 12.7252 13.1004 12.4766C13.1004 12.2281 12.8989 12.0266 12.6504 12.0266V12.9266ZM9.50039 12.5001C9.50039 12.5021 9.50259 12.4522 9.58114 12.3303C9.65564 12.2148 9.77189 12.0753 9.93149 11.9071C10.2683 11.552 10.6665 11.1985 11.1441 10.7188C12.025 9.83402 13.05 8.63938 13.05 7.20007H12.15C12.15 8.23064 11.4002 9.18601 10.5063 10.0838C10.0965 10.4953 9.60724 10.9412 9.27855 11.2877C9.10537 11.4702 8.9443 11.6571 8.82469 11.8427C8.70912 12.022 8.60039 12.248 8.60039 12.5001H9.50039ZM13.05 7.20007C13.05 6.48662 12.8238 5.90977 12.4397 5.51106C12.0576 5.11452 11.5464 4.92376 11.0379 4.9374C9.99158 4.96547 9.05 5.8345 9.05 7.29992H9.95C9.95 6.26498 10.5584 5.85059 11.0621 5.83708C11.3286 5.82993 11.5924 5.92884 11.7916 6.13554C11.9887 6.34007 12.15 6.68002 12.15 7.20007H13.05ZM9.60031 12.5001C9.60031 12.4887 9.60679 12.4324 9.67486 12.315C9.74052 12.2017 9.84384 12.0647 9.98776 11.9002C10.2793 11.567 10.6742 11.1901 11.1278 10.7356L10.4908 10.0998C10.0625 10.5288 9.62789 10.9447 9.31041 11.3076C9.14982 11.4911 9.00376 11.678 8.89616 11.8637C8.79096 12.0453 8.70031 12.2631 8.70031 12.5001H9.60031ZM11.1278 10.7356C12.0133 9.84842 13.05 8.64775 13.05 7.20007H12.15C12.15 8.23736 11.3909 9.198 10.4908 10.0998L11.1278 10.7356ZM9 12.9266H12.6504V12.0266H9V12.9266Z"
        />
        <path
            className={letterClassName}
            fillRule="evenodd"
            clipRule="evenodd"
            d="M8.30469 2.50078C8.30469 2.25225 8.50616 2.05078 8.75469 2.05078H12.2547C12.5032 2.05078 12.7047 2.25225 12.7047 2.50078C12.7047 2.74931 12.5032 2.95078 12.2547 2.95078H11.0503V4.00039C11.0336 4.00013 11.0168 4 11 4C10.6368 4 10.2848 4.06052 9.95034 4.17393V2.95078H8.75469C8.50616 2.95078 8.30469 2.74931 8.30469 2.50078ZM7.31541 7.05062H5.05034V2.95078H6.25469C6.50322 2.95078 6.70469 2.74931 6.70469 2.50078C6.70469 2.25225 6.50322 2.05078 6.25469 2.05078H2.75469C2.50616 2.05078 2.30469 2.25225 2.30469 2.50078C2.30469 2.74931 2.50616 2.95078 2.75469 2.95078H3.95034V12.0508H2.75469C2.50616 12.0508 2.30469 12.2523 2.30469 12.5008C2.30469 12.7493 2.50616 12.9508 2.75469 12.9508H6.25469C6.50322 12.9508 6.70469 12.7493 6.70469 12.5008C6.70469 12.2523 6.50322 12.0508 6.25469 12.0508H5.05034V7.95062H7.08824C7.14151 7.63886 7.21802 7.33787 7.31541 7.05062Z"
        />
    </svg>
);

export default H2Icon;
