{"name": "@onlook/ui", "description": "A ui library for Onlook", "version": "0.0.0", "private": true, "type": "module", "main": "src/index.ts", "module": "src/index.ts", "types": "src/index.ts", "repository": {"type": "git", "url": "https://github.com/onlook-dev/desktop.git"}, "files": ["tokens.ts", "tailwind.config.ts", "postcss.config.js", "globals.css"], "scripts": {"clean": "rm -rf node_modules", "lint": "eslint --fix .", "format": "prettier --write .", "typecheck": "tsc --noEmit"}, "keywords": ["onlook", "ui"], "author": {"name": "Onlook", "email": "<EMAIL>"}, "license": "Apache-2.0", "homepage": "https://onlook.com", "devDependencies": {"@onlook/utility": "*", "@onlook/typescript": "*", "@types/color-namer": "^1.3.3", "autoprefixer": "^10.4.20", "react": "^18.3.1", "react-dom": "^18.3.1", "typescript": "^5.5.4"}, "exports": {"./*": "./src/components/*.tsx", "./icons": "./src/components/icons/index.tsx", "./color-picker": "./src/components/color-picker/index.tsx", "./tokens": "./tokens.ts", "./tailwind.config": "./tailwind.config.ts", "./postcss": "./postcss.config.js", "./globals.css": "./src/globals.css", "./utils": "./src/utils/index.ts", "./hooks": "./src/hooks/index.ts"}, "dependencies": {"@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0", "@onlook/foundation": "*", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-collapsible": "^1.1.2", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-scroll-area": "^1.2.0", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.2", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "1.0.4", "color-namer": "^1.4.0", "css-color-names": "^1.0.1", "motion": "^11.15.0", "parse-css-color": "^0.2.1", "prosemirror-view": "^1.34.2", "react-merge-refs": "^2.1.1", "tailwind-merge": "^2.3.0", "tailwind-styled-components": "^2.2.0", "tailwindcss-animate": "^1.0.7", "use-eye-dropper": "^1.6.4"}}