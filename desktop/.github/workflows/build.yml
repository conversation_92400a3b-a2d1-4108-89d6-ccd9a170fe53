name: Build

on:
    push:
        branches:
            - 'main'
        tags:
            - 'v[0-9]+.[0-9]+.[0-9]+'
            - 'v[0-9]+.[0-9]+.[0-9]+-**'
        paths-ignore:
            - '**.md'
            - '**.spec.js'
            - '.idea'
            - '.vscode'
            - '.dockerignore'
            - 'Dockerfile'
            - '.gitignore'

jobs:
    build:
        runs-on: ${{ matrix.os }}

        permissions:
            contents: write

        strategy:
            fail-fast: false
            matrix:
                include:
                    - os: macos-latest
                    - os: windows-latest
                    - os: ubuntu-latest

        steps:
            - name: Checkout code
              uses: actions/checkout@v4

            - name: Setup Node.js
              uses: actions/setup-node@v4
              with:
                  node-version: 20

            - name: Install node-gyp dependencies
              shell: bash
              run: |
                  if [ "$RUNNER_OS" == "macOS" ]; then
                      brew install python-setuptools
                  fi

            - name: Set Mac API key
              if: startsWith(matrix.os, 'macos')
              run: |
                  mkdir -p ~/private_keys/
                  echo '${{ secrets.APPLE_API_KEY_CONTENT }}' > ~/private_keys/AuthKey_${{ secrets.APPLE_API_KEY_ID }}.p8

            - uses: oven-sh/setup-bun@v1
              with:
                  bun-version: 1.2.8

            - name: Install dependencies
              run: bun install

            - name: Download Bun binary
              env:
                  CI_ARCH: ${{ matrix.arch }}
              run: bun run download_bun

            - name: Set environment variables
              shell: bash
              run: |
                  echo "VITE_SUPABASE_API_URL=${{ secrets.SUPABASE_API_URL }}" >> $GITHUB_ENV
                  echo "VITE_SUPABASE_ANON_KEY=${{ secrets.SUPABASE_ANON_KEY }}" >> $GITHUB_ENV
                  echo "VITE_MIXPANEL_TOKEN=${{ secrets.MIXPANEL_TOKEN }}" >> $GITHUB_ENV

            - name: Build foundation
              run: bun run ci:build

            - name: Build and Release
              uses: samuelmeuli/action-electron-builder@v1
              with:
                  skip_build: true
                  package_root: apps/studio
                  github_token: ${{ secrets.GITHUB_TOKEN }}
                  release: ${{ startsWith(github.ref, 'refs/tags/v') }}
                  mac_certs: ${{ secrets.MAC_CERTS }}
                  mac_certs_password: ${{ secrets.MAC_CERTS_PASSWORD }}
                  args: ${{ startsWith(matrix.os, 'macos') && '--x64 --arm64' || '' }} --config builder-config/base.ts
              env:
                  GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
                  APPLE_API_KEY: ${{ startsWith(matrix.os, 'macos') && format('~/private_keys/AuthKey_{0}.p8', secrets.APPLE_API_KEY_ID) || '' }}
                  APPLE_API_KEY_ID: ${{ secrets.APPLE_API_KEY_ID }}
                  APPLE_API_ISSUER: ${{ secrets.APPLE_API_ISSUER }}
                  AZURE_TENANT_ID: ${{ secrets.AZURE_TENANT_ID }}
                  AZURE_CLIENT_ID: ${{ secrets.AZURE_CLIENT_ID }}
                  AZURE_CLIENT_SECRET: ${{ secrets.AZURE_CLIENT_SECRET }}
