{"productName": "Onlook", "name": "@onlook/studio", "version": "0.2.31", "homepage": "https://onlook.com", "main": "dist-electron/main/index.js", "description": "The first-ever devtool for designers", "license": "Apache-2.0", "author": {"name": "Onlook", "email": "<EMAIL>"}, "private": true, "repository": {"type": "git", "url": "https://github.com/onlook-dev/desktop.git"}, "debug": {"env": {"VITE_DEV_SERVER_URL": "http://127.0.0.1:7777/"}}, "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "clean": "git clean -xdf dist-electron node_modules", "package": "electron-builder --config builder-config/base.ts", "preview": "vite preview", "pree2e": "vite build --mode=test", "e2e": "playwright test", "test": "bun test", "coverage": "bun test --coverage", "lint": "eslint --fix .", "format": "prettier --write .", "typecheck": "tsc --noEmit"}, "dependencies": {"@ai-sdk/anthropic": "^1.1.17", "@codemirror/autocomplete": "^6.18.6", "@codemirror/closebrackets": "^0.19.2", "@codemirror/lang-css": "^6.0.0", "@codemirror/lang-html": "^6.0.0", "@codemirror/lang-javascript": "^6.0.0", "@codemirror/lang-json": "^6.0.0", "@codemirror/lang-markdown": "^6.3.2", "@codemirror/lint": "^6.8.5", "@codemirror/matchbrackets": "^0.19.4", "@codemirror/rectangular-selection": "^0.19.2", "@codemirror/search": "^6.5.10", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.0.0", "@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0", "@fontsource-variable/inter": "^5.1.0", "@onlook/foundation": "*", "@onlook/supabase": "*", "@onlook/ui": "*", "@parcel/watcher": "^2.5.1", "@shikijs/monaco": "^1.22.0", "@supabase/supabase-js": "^2.45.6", "@uiw/react-codemirror": "^4.21.21", "@trainloop/sdk": "^0.1.8", "@types/webfontloader": "^1.6.38", "@xterm/xterm": "^5.6.0-beta.98", "ai": "^4.2.6", "browser-image-compression": "^2.0.2", "detect-port": "^2.1.0", "download": "^8.0.0", "electron-log": "^5.2.0", "electron-updater": "^6.3.4", "embla-carousel-react": "^8.3.0", "embla-carousel-wheel-gestures": "^8.0.1", "fflate": "^0.8.2", "fix-path": "^4.0.0", "flexsearch": "^0.8.151", "freestyle-sandboxes": "^0.0.36", "i18next": "^24.2.2", "istextorbinary": "^9.5.0", "js-string-escape": "^1.0.1", "lodash-es": "^4.17.21", "mime-lite": "^1.0.3", "mixpanel": "^0.18.0", "monaco-editor": "^0.52.0", "motion": "^11.17.0", "nanoid": "^5.0.7", "node-pty": "1.1.0-beta30", "prosemirror-commands": "^1.6.0", "prosemirror-history": "^1.4.1", "prosemirror-keymap": "^1.2.2", "prosemirror-model": "^1.22.3", "prosemirror-state": "^1.4.3", "prosemirror-view": "^1.34.2", "react-arborist": "^3.4.2", "react-diff-viewer-continued": "^3.4.0", "react-hotkeys-hook": "^4.5.0", "react-i18next": "^15.4.0", "react-markdown": "^9.0.1", "remark-gfm": "^4.0.0", "shell-quote": "^1.8.2", "shiki": "^1.22.0", "ts-morph": "^25.0.0", "type-fest": "^4.26.1", "use-resize-observer": "^9.1.0", "webfontloader": "^1.6.28", "zod": "^3.23.8"}, "devDependencies": {"@eslint/js": "^9.7.0", "@onlook/typescript": "*", "@playwright/test": "^1.42.1", "@tailwindcss/typography": "^0.5.15", "@types/babel-generator": "^6.25.8", "@types/babel__traverse": "^7.20.6", "@types/bun": "^1.1.6", "@types/css-tree": "^2.3.8", "@types/download": "^8.0.5", "@types/flexsearch": "^0.7.42", "@types/istextorbinary": "^2.3.4", "@types/js-string-escape": "^1.0.3", "@types/lodash": "^4.17.7", "@types/node": "^22.8.6", "@types/react": "^18.2.64", "@types/react-dom": "^18.2.21", "@types/shell-quote": "^1.7.5", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.20", "css-tree": "^3.1.0", "electron": "36.0.1", "electron-builder": "^26.0.11", "eslint": "9.x", "eslint-config-prettier": "^9.1.0", "eslint-plugin-react": "^7.34.3", "glob": "^11.0.1", "globals": "^15.8.0", "mobx": "^6.12.4", "mobx-react-lite": "^4.0.7", "postcss": "^8.5.3", "postcss-import": "^16.0.1", "prettier": "^3.3.2", "react": "^18.3.1", "react-dom": "^18.3.1", "tailwindcss": "^3.4.4", "typescript": "^5.5.4", "typescript-eslint": "^8.16.0", "vite": "^6.0.11", "vite-plugin-electron": "^0.29.0", "vite-plugin-electron-renderer": "^0.14.5"}}