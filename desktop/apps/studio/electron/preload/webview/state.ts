import { WebviewChannels } from '@onlook/models/constants';
import { ipc<PERSON><PERSON><PERSON> } from 'electron';

export function setWebviewId(webviewId: string) {
    (window as any)._onlookWebviewId = webviewId;
}

export function getWebviewId(): string {
    const webviewId = (window as any)._onlookWebviewId;
    if (!webviewId) {
        console.warn('Webview id not found');
        ipcRenderer.sendToHost(WebviewChannels.GET_WEBVIEW_ID);
        return '';
    }
    return webviewId;
}
