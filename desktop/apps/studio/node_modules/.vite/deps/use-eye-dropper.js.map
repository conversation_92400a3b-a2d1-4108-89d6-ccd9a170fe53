{"version": 3, "sources": ["../../../../../node_modules/use-eye-dropper/lib/use-eye-dropper.module.dev.js", "../../../../../node_modules/use-eye-dropper/lib/use-eye-dropper.module.js", "../../../../../node_modules/use-eye-dropper/lib/index.module.js"], "sourcesContent": ["import{use<PERSON>emo as o,useRef as r,use<PERSON>allback as e,useEffect as n,useState as t}from\"react\";const p=()=>\"undefined\"!=typeof window&&\"EyeDropper\"in window,s=()=>{let o=\"Unsupported browser.\";throw o=\"Unsupported browser: no EyeDropper in Window. Check https://developer.mozilla.org/en-US/docs/Web/API/EyeDropper_API.\",new Error(\"Unsupported browser: no EyeDropper in Window. Check https://developer.mozilla.org/en-US/docs/Web/API/EyeDropper_API.\")},i=i=>{const c=o(()=>{var o;return(o=p()&&new EyeDropper(i))?EyeDropper.prototype.open.bind(o):s},[i]),[a,d]=(()=>{const o=r(),[s,i]=t(!1);n(()=>(o.current=!0,i(p()),()=>{o.current=!1}),[]);const c=e(()=>s,[s]);return[o,c]})(),l=r(),w=e(()=>{void 0!==l.current&&l.current.abort()},[l]),u=e(async function(o){void 0===o&&(o={}),w();const{signal:r,...e}=o,n=new AbortController;l.current=n;const t=void 0!==r?(o=>{if(\"any\"in AbortSignal)return AbortSignal.any(o);const r=new AbortController,e=()=>{r.abort();for(const r of o)r.removeEventListener(\"abort\",e)};for(const r of o){if(r.aborted){e();break}r.addEventListener(\"abort\",e)}return r.signal})([r,n.signal]):n.signal;try{return await c({...e,signal:t})}catch(o){throw a.current||(o.canceled=!0),o}},[l,a,w,c]);return n(()=>w,[w]),{open:u,close:w,isSupported:d}};export{i as default};\n", "import{use<PERSON>emo as r,useRef as o,use<PERSON><PERSON>back as n,useEffect as t,useState as e}from\"react\";const i=()=>\"undefined\"!=typeof window&&\"EyeDropper\"in window,s=()=>{throw new Error(\"Unsupported browser.\")},a=a=>{const c=r(()=>{var r;return(r=i()&&new EyeDropper(a))?EyeDropper.prototype.open.bind(r):s},[a]),[p,w]=(()=>{const r=o(),[s,a]=e(!1);t(()=>(r.current=!0,a(i()),()=>{r.current=!1}),[]);const c=n(()=>s,[s]);return[r,c]})(),f=o(),u=n(()=>{void 0!==f.current&&f.current.abort()},[f]),d=n(async function(r){void 0===r&&(r={}),u();const{signal:o,...n}=r,t=new AbortController;f.current=t;const e=void 0!==o?(r=>{if(\"any\"in AbortSignal)return AbortSignal.any(r);const o=new AbortController,n=()=>{o.abort();for(const o of r)o.removeEventListener(\"abort\",n)};for(const o of r){if(o.aborted){n();break}o.addEventListener(\"abort\",n)}return o.signal})([o,t.signal]):t.signal;try{return await c({...n,signal:e})}catch(r){throw p.current||(r.canceled=!0),r}},[f,p,u,c]);return t(()=>u,[u]),{open:d,close:u,isSupported:w}};export{a as default};\n", "import { default as DropperDev } from './use-eye-dropper.module.dev.js'\nimport { default as Dropper } from './use-eye-dropper.module.js'\n\nexport default process.env.NODE_ENV === 'production' ? Dropper : DropperDev\n"], "mappings": ";;;;;;;;AAAA,mBAAkF;AAAQ,IAAM,IAAE,MAAI,eAAa,OAAO,UAAQ,gBAAe;AAAvD,IAA8D,IAAE,MAAI;AAAC,MAAIA,KAAE;AAAuB,QAAMA,KAAE,wHAAuH,IAAI,MAAM,sHAAsH;AAAC;AAAlW,IAAoW,IAAE,CAAAC,OAAG;AAAC,QAAM,QAAE,aAAAD,SAAE,MAAI;AAAC,QAAIA;AAAE,YAAOA,KAAE,EAAE,KAAG,IAAI,WAAWC,EAAC,KAAG,WAAW,UAAU,KAAK,KAAKD,EAAC,IAAE;AAAA,EAAC,GAAE,CAACC,EAAC,CAAC,GAAE,CAACC,IAAE,CAAC,KAAG,MAAI;AAAC,UAAMF,SAAE,aAAAG,QAAE,GAAE,CAACC,IAAEH,EAAC,QAAE,aAAAI,UAAE,KAAE;AAAE,qBAAAC,WAAE,OAAKN,GAAE,UAAQ,MAAGC,GAAE,EAAE,CAAC,GAAE,MAAI;AAAC,MAAAD,GAAE,UAAQ;AAAA,IAAE,IAAG,CAAC,CAAC;AAAE,UAAMO,SAAE,aAAAC,aAAE,MAAIJ,IAAE,CAACA,EAAC,CAAC;AAAE,WAAM,CAACJ,IAAEO,EAAC;AAAA,EAAC,GAAG,GAAE,QAAE,aAAAJ,QAAE,GAAE,QAAE,aAAAK,aAAE,MAAI;AAAC,eAAS,EAAE,WAAS,EAAE,QAAQ,MAAM;AAAA,EAAC,GAAE,CAAC,CAAC,CAAC,GAAE,QAAE,aAAAA,aAAE,eAAeR,IAAE;AAAC,eAASA,OAAIA,KAAE,CAAC,IAAG,EAAE;AAAE,UAAK,EAAC,QAAOG,IAAE,GAAGK,GAAC,IAAER,IAAEM,KAAE,IAAI;AAAgB,MAAE,UAAQA;AAAE,UAAMD,KAAE,WAASF,MAAG,CAAAH,OAAG;AAAC,UAAG,SAAQ,YAAY,QAAO,YAAY,IAAIA,EAAC;AAAE,YAAMG,KAAE,IAAI,mBAAgBK,KAAE,MAAI;AAAC,QAAAL,GAAE,MAAM;AAAE,mBAAUA,MAAKH,GAAE,CAAAG,GAAE,oBAAoB,SAAQK,EAAC;AAAA,MAAC;AAAE,iBAAUL,MAAKH,IAAE;AAAC,YAAGG,GAAE,SAAQ;AAAC,UAAAK,GAAE;AAAE;AAAA,QAAK;AAAC,QAAAL,GAAE,iBAAiB,SAAQK,EAAC;AAAA,MAAC;AAAC,aAAOL,GAAE;AAAA,IAAM,GAAG,CAACA,IAAEG,GAAE,MAAM,CAAC,IAAEA,GAAE;AAAO,QAAG;AAAC,aAAO,MAAM,EAAE,EAAC,GAAGE,IAAE,QAAOH,GAAC,CAAC;AAAA,IAAC,SAAOL,IAAE;AAAC,YAAME,GAAE,YAAUF,GAAE,WAAS,OAAIA;AAAA,IAAC;AAAA,EAAC,GAAE,CAAC,GAAEE,IAAE,GAAE,CAAC,CAAC;AAAE,aAAO,aAAAI,WAAE,MAAI,GAAE,CAAC,CAAC,CAAC,GAAE,EAAC,MAAK,GAAE,OAAM,GAAE,aAAY,EAAC;AAAC;;;ACA1uC,IAAAG,gBAAkF;;;ACGlF,IAAO,uBAAQ,QAAwC,IAAU;", "names": ["o", "i", "a", "r", "s", "t", "n", "c", "e", "import_react"]}