{"version": 3, "sources": ["../../../../../node_modules/use-resize-observer/dist/bundle.esm.js"], "sourcesContent": ["import { useRef, useEffect, useCallback, useState, useMemo } from 'react';\n\n// This could've been more streamlined with internal state instead of abusing\n// refs to such extent, but then composing hooks and components could not opt out of unnecessary renders.\nfunction useResolvedElement(subscriber, refOrElement) {\n  var lastReportRef = useRef(null);\n  var refOrElementRef = useRef(null);\n  refOrElementRef.current = refOrElement;\n  var cbElementRef = useRef(null); // Calling re-evaluation after each render without using a dep array,\n  // as the ref object's current value could've changed since the last render.\n\n  useEffect(function () {\n    evaluateSubscription();\n  });\n  var evaluateSubscription = useCallback(function () {\n    var cbElement = cbElementRef.current;\n    var refOrElement = refOrElementRef.current; // Ugly ternary. But smaller than an if-else block.\n\n    var element = cbElement ? cbElement : refOrElement ? refOrElement instanceof Element ? refOrElement : refOrElement.current : null;\n\n    if (lastReportRef.current && lastReportRef.current.element === element && lastReportRef.current.subscriber === subscriber) {\n      return;\n    }\n\n    if (lastReportRef.current && lastReportRef.current.cleanup) {\n      lastReportRef.current.cleanup();\n    }\n\n    lastReportRef.current = {\n      element: element,\n      subscriber: subscriber,\n      // Only calling the subscriber, if there's an actual element to report.\n      // Setting cleanup to undefined unless a subscriber returns one, as an existing cleanup function would've been just called.\n      cleanup: element ? subscriber(element) : undefined\n    };\n  }, [subscriber]); // making sure we call the cleanup function on unmount\n\n  useEffect(function () {\n    return function () {\n      if (lastReportRef.current && lastReportRef.current.cleanup) {\n        lastReportRef.current.cleanup();\n        lastReportRef.current = null;\n      }\n    };\n  }, []);\n  return useCallback(function (element) {\n    cbElementRef.current = element;\n    evaluateSubscription();\n  }, [evaluateSubscription]);\n}\n\n// We're only using the first element of the size sequences, until future versions of the spec solidify on how\n// exactly it'll be used for fragments in multi-column scenarios:\n// From the spec:\n// > The box size properties are exposed as FrozenArray in order to support elements that have multiple fragments,\n// > which occur in multi-column scenarios. However the current definitions of content rect and border box do not\n// > mention how those boxes are affected by multi-column layout. In this spec, there will only be a single\n// > ResizeObserverSize returned in the FrozenArray, which will correspond to the dimensions of the first column.\n// > A future version of this spec will extend the returned FrozenArray to contain the per-fragment size information.\n// (https://drafts.csswg.org/resize-observer/#resize-observer-entry-interface)\n//\n// Also, testing these new box options revealed that in both Chrome and FF everything is returned in the callback,\n// regardless of the \"box\" option.\n// The spec states the following on this:\n// > This does not have any impact on which box dimensions are returned to the defined callback when the event\n// > is fired, it solely defines which box the author wishes to observe layout changes on.\n// (https://drafts.csswg.org/resize-observer/#resize-observer-interface)\n// I'm not exactly clear on what this means, especially when you consider a later section stating the following:\n// > This section is non-normative. An author may desire to observe more than one CSS box.\n// > In this case, author will need to use multiple ResizeObservers.\n// (https://drafts.csswg.org/resize-observer/#resize-observer-interface)\n// Which is clearly not how current browser implementations behave, and seems to contradict the previous quote.\n// For this reason I decided to only return the requested size,\n// even though it seems we have access to results for all box types.\n// This also means that we get to keep the current api, being able to return a simple { width, height } pair,\n// regardless of box option.\nfunction extractSize(entry, boxProp, sizeType) {\n  if (!entry[boxProp]) {\n    if (boxProp === \"contentBoxSize\") {\n      // The dimensions in `contentBoxSize` and `contentRect` are equivalent according to the spec.\n      // See the 6th step in the description for the RO algorithm:\n      // https://drafts.csswg.org/resize-observer/#create-and-populate-resizeobserverentry-h\n      // > Set this.contentRect to logical this.contentBoxSize given target and observedBox of \"content-box\".\n      // In real browser implementations of course these objects differ, but the width/height values should be equivalent.\n      return entry.contentRect[sizeType === \"inlineSize\" ? \"width\" : \"height\"];\n    }\n\n    return undefined;\n  } // A couple bytes smaller than calling Array.isArray() and just as effective here.\n\n\n  return entry[boxProp][0] ? entry[boxProp][0][sizeType] : // TS complains about this, because the RO entry type follows the spec and does not reflect Firefox's current\n  // behaviour of returning objects instead of arrays for `borderBoxSize` and `contentBoxSize`.\n  // @ts-ignore\n  entry[boxProp][sizeType];\n}\n\nfunction useResizeObserver(opts) {\n  if (opts === void 0) {\n    opts = {};\n  }\n\n  // Saving the callback as a ref. With this, I don't need to put onResize in the\n  // effect dep array, and just passing in an anonymous function without memoising\n  // will not reinstantiate the hook's ResizeObserver.\n  var onResize = opts.onResize;\n  var onResizeRef = useRef(undefined);\n  onResizeRef.current = onResize;\n  var round = opts.round || Math.round; // Using a single instance throughout the hook's lifetime\n\n  var resizeObserverRef = useRef();\n\n  var _useState = useState({\n    width: undefined,\n    height: undefined\n  }),\n      size = _useState[0],\n      setSize = _useState[1]; // In certain edge cases the RO might want to report a size change just after\n  // the component unmounted.\n\n\n  var didUnmount = useRef(false);\n  useEffect(function () {\n    didUnmount.current = false;\n    return function () {\n      didUnmount.current = true;\n    };\n  }, []); // Using a ref to track the previous width / height to avoid unnecessary renders.\n\n  var previous = useRef({\n    width: undefined,\n    height: undefined\n  }); // This block is kinda like a useEffect, only it's called whenever a new\n  // element could be resolved based on the ref option. It also has a cleanup\n  // function.\n\n  var refCallback = useResolvedElement(useCallback(function (element) {\n    // We only use a single Resize Observer instance, and we're instantiating it on demand, only once there's something to observe.\n    // This instance is also recreated when the `box` option changes, so that a new observation is fired if there was a previously observed element with a different box option.\n    if (!resizeObserverRef.current || resizeObserverRef.current.box !== opts.box || resizeObserverRef.current.round !== round) {\n      resizeObserverRef.current = {\n        box: opts.box,\n        round: round,\n        instance: new ResizeObserver(function (entries) {\n          var entry = entries[0];\n          var boxProp = opts.box === \"border-box\" ? \"borderBoxSize\" : opts.box === \"device-pixel-content-box\" ? \"devicePixelContentBoxSize\" : \"contentBoxSize\";\n          var reportedWidth = extractSize(entry, boxProp, \"inlineSize\");\n          var reportedHeight = extractSize(entry, boxProp, \"blockSize\");\n          var newWidth = reportedWidth ? round(reportedWidth) : undefined;\n          var newHeight = reportedHeight ? round(reportedHeight) : undefined;\n\n          if (previous.current.width !== newWidth || previous.current.height !== newHeight) {\n            var newSize = {\n              width: newWidth,\n              height: newHeight\n            };\n            previous.current.width = newWidth;\n            previous.current.height = newHeight;\n\n            if (onResizeRef.current) {\n              onResizeRef.current(newSize);\n            } else {\n              if (!didUnmount.current) {\n                setSize(newSize);\n              }\n            }\n          }\n        })\n      };\n    }\n\n    resizeObserverRef.current.instance.observe(element, {\n      box: opts.box\n    });\n    return function () {\n      if (resizeObserverRef.current) {\n        resizeObserverRef.current.instance.unobserve(element);\n      }\n    };\n  }, [opts.box, round]), opts.ref);\n  return useMemo(function () {\n    return {\n      ref: refCallback,\n      width: size.width,\n      height: size.height\n    };\n  }, [refCallback, size.width, size.height]);\n}\n\nexport { useResizeObserver as default };\n"], "mappings": ";;;;;;;;AAAA,mBAAkE;AAIlE,SAAS,mBAAmB,YAAY,cAAc;AACpD,MAAI,oBAAgB,qBAAO,IAAI;AAC/B,MAAI,sBAAkB,qBAAO,IAAI;AACjC,kBAAgB,UAAU;AAC1B,MAAI,mBAAe,qBAAO,IAAI;AAG9B,8BAAU,WAAY;AACpB,yBAAqB;AAAA,EACvB,CAAC;AACD,MAAI,2BAAuB,0BAAY,WAAY;AACjD,QAAI,YAAY,aAAa;AAC7B,QAAIA,gBAAe,gBAAgB;AAEnC,QAAI,UAAU,YAAY,YAAYA,gBAAeA,yBAAwB,UAAUA,gBAAeA,cAAa,UAAU;AAE7H,QAAI,cAAc,WAAW,cAAc,QAAQ,YAAY,WAAW,cAAc,QAAQ,eAAe,YAAY;AACzH;AAAA,IACF;AAEA,QAAI,cAAc,WAAW,cAAc,QAAQ,SAAS;AAC1D,oBAAc,QAAQ,QAAQ;AAAA,IAChC;AAEA,kBAAc,UAAU;AAAA,MACtB;AAAA,MACA;AAAA;AAAA;AAAA,MAGA,SAAS,UAAU,WAAW,OAAO,IAAI;AAAA,IAC3C;AAAA,EACF,GAAG,CAAC,UAAU,CAAC;AAEf,8BAAU,WAAY;AACpB,WAAO,WAAY;AACjB,UAAI,cAAc,WAAW,cAAc,QAAQ,SAAS;AAC1D,sBAAc,QAAQ,QAAQ;AAC9B,sBAAc,UAAU;AAAA,MAC1B;AAAA,IACF;AAAA,EACF,GAAG,CAAC,CAAC;AACL,aAAO,0BAAY,SAAU,SAAS;AACpC,iBAAa,UAAU;AACvB,yBAAqB;AAAA,EACvB,GAAG,CAAC,oBAAoB,CAAC;AAC3B;AA2BA,SAAS,YAAY,OAAO,SAAS,UAAU;AAC7C,MAAI,CAAC,MAAM,OAAO,GAAG;AACnB,QAAI,YAAY,kBAAkB;AAMhC,aAAO,MAAM,YAAY,aAAa,eAAe,UAAU,QAAQ;AAAA,IACzE;AAEA,WAAO;AAAA,EACT;AAGA,SAAO,MAAM,OAAO,EAAE,CAAC,IAAI,MAAM,OAAO,EAAE,CAAC,EAAE,QAAQ;AAAA;AAAA;AAAA;AAAA,IAGrD,MAAM,OAAO,EAAE,QAAQ;AAAA;AACzB;AAEA,SAAS,kBAAkB,MAAM;AAC/B,MAAI,SAAS,QAAQ;AACnB,WAAO,CAAC;AAAA,EACV;AAKA,MAAI,WAAW,KAAK;AACpB,MAAI,kBAAc,qBAAO,MAAS;AAClC,cAAY,UAAU;AACtB,MAAI,QAAQ,KAAK,SAAS,KAAK;AAE/B,MAAI,wBAAoB,qBAAO;AAE/B,MAAI,gBAAY,uBAAS;AAAA,IACvB,OAAO;AAAA,IACP,QAAQ;AAAA,EACV,CAAC,GACG,OAAO,UAAU,CAAC,GAClB,UAAU,UAAU,CAAC;AAIzB,MAAI,iBAAa,qBAAO,KAAK;AAC7B,8BAAU,WAAY;AACpB,eAAW,UAAU;AACrB,WAAO,WAAY;AACjB,iBAAW,UAAU;AAAA,IACvB;AAAA,EACF,GAAG,CAAC,CAAC;AAEL,MAAI,eAAW,qBAAO;AAAA,IACpB,OAAO;AAAA,IACP,QAAQ;AAAA,EACV,CAAC;AAID,MAAI,cAAc,uBAAmB,0BAAY,SAAU,SAAS;AAGlE,QAAI,CAAC,kBAAkB,WAAW,kBAAkB,QAAQ,QAAQ,KAAK,OAAO,kBAAkB,QAAQ,UAAU,OAAO;AACzH,wBAAkB,UAAU;AAAA,QAC1B,KAAK,KAAK;AAAA,QACV;AAAA,QACA,UAAU,IAAI,eAAe,SAAU,SAAS;AAC9C,cAAI,QAAQ,QAAQ,CAAC;AACrB,cAAI,UAAU,KAAK,QAAQ,eAAe,kBAAkB,KAAK,QAAQ,6BAA6B,8BAA8B;AACpI,cAAI,gBAAgB,YAAY,OAAO,SAAS,YAAY;AAC5D,cAAI,iBAAiB,YAAY,OAAO,SAAS,WAAW;AAC5D,cAAI,WAAW,gBAAgB,MAAM,aAAa,IAAI;AACtD,cAAI,YAAY,iBAAiB,MAAM,cAAc,IAAI;AAEzD,cAAI,SAAS,QAAQ,UAAU,YAAY,SAAS,QAAQ,WAAW,WAAW;AAChF,gBAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,QAAQ;AAAA,YACV;AACA,qBAAS,QAAQ,QAAQ;AACzB,qBAAS,QAAQ,SAAS;AAE1B,gBAAI,YAAY,SAAS;AACvB,0BAAY,QAAQ,OAAO;AAAA,YAC7B,OAAO;AACL,kBAAI,CAAC,WAAW,SAAS;AACvB,wBAAQ,OAAO;AAAA,cACjB;AAAA,YACF;AAAA,UACF;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AAEA,sBAAkB,QAAQ,SAAS,QAAQ,SAAS;AAAA,MAClD,KAAK,KAAK;AAAA,IACZ,CAAC;AACD,WAAO,WAAY;AACjB,UAAI,kBAAkB,SAAS;AAC7B,0BAAkB,QAAQ,SAAS,UAAU,OAAO;AAAA,MACtD;AAAA,IACF;AAAA,EACF,GAAG,CAAC,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,GAAG;AAC/B,aAAO,sBAAQ,WAAY;AACzB,WAAO;AAAA,MACL,KAAK;AAAA,MACL,OAAO,KAAK;AAAA,MACZ,QAAQ,KAAK;AAAA,IACf;AAAA,EACF,GAAG,CAAC,aAAa,KAAK,OAAO,KAAK,MAAM,CAAC;AAC3C;", "names": ["refOrElement"]}