import {
  require_react
} from "./chunk-AAFIJYSU.js";
import {
  __toESM
} from "./chunk-5IKWDFCZ.js";

// ../../node_modules/use-eye-dropper/lib/use-eye-dropper.module.dev.js
var import_react = __toESM(require_react());
var p = () => "undefined" != typeof window && "EyeDropper" in window;
var s = () => {
  let o3 = "Unsupported browser.";
  throw o3 = "Unsupported browser: no EyeDropper in Window. Check https://developer.mozilla.org/en-US/docs/Web/API/EyeDropper_API.", new Error("Unsupported browser: no EyeDropper in Window. Check https://developer.mozilla.org/en-US/docs/Web/API/EyeDropper_API.");
};
var i = (i2) => {
  const c = (0, import_react.useMemo)(() => {
    var o3;
    return (o3 = p() && new EyeDropper(i2)) ? EyeDropper.prototype.open.bind(o3) : s;
  }, [i2]), [a2, d] = (() => {
    const o3 = (0, import_react.useRef)(), [s2, i3] = (0, import_react.useState)(false);
    (0, import_react.useEffect)(() => (o3.current = true, i3(p()), () => {
      o3.current = false;
    }), []);
    const c2 = (0, import_react.useCallback)(() => s2, [s2]);
    return [o3, c2];
  })(), l = (0, import_react.useRef)(), w = (0, import_react.useCallback)(() => {
    void 0 !== l.current && l.current.abort();
  }, [l]), u = (0, import_react.useCallback)(async function(o3) {
    void 0 === o3 && (o3 = {}), w();
    const { signal: r3, ...e3 } = o3, n3 = new AbortController();
    l.current = n3;
    const t3 = void 0 !== r3 ? ((o4) => {
      if ("any" in AbortSignal) return AbortSignal.any(o4);
      const r4 = new AbortController(), e4 = () => {
        r4.abort();
        for (const r5 of o4) r5.removeEventListener("abort", e4);
      };
      for (const r5 of o4) {
        if (r5.aborted) {
          e4();
          break;
        }
        r5.addEventListener("abort", e4);
      }
      return r4.signal;
    })([r3, n3.signal]) : n3.signal;
    try {
      return await c({ ...e3, signal: t3 });
    } catch (o4) {
      throw a2.current || (o4.canceled = true), o4;
    }
  }, [l, a2, w, c]);
  return (0, import_react.useEffect)(() => w, [w]), { open: u, close: w, isSupported: d };
};

// ../../node_modules/use-eye-dropper/lib/use-eye-dropper.module.js
var import_react2 = __toESM(require_react());

// ../../node_modules/use-eye-dropper/lib/index.module.js
var index_module_default = false ? a : i;
export {
  index_module_default as default
};
//# sourceMappingURL=use-eye-dropper.js.map
