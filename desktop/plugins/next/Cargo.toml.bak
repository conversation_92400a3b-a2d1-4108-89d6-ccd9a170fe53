[package]
authors = ["Ki<PERSON> Ho"]
description = "SWC plugin for onlook"
edition = "2021"
license = "Apache-2.0"
name = "swc_plugin_onlook"
publish = false
version = "1.0.0"
categories = ["development-tools"] # You can find the supported categories here https://crates.io/category_slugs
keywords = ["development-tools"]
homepage = "https://github.com/Kitenite/onlook.libs.git"
readme = "README.md"
repository = "https://github.com/Kitenite/onlook.libs.git"
exclude = ["Cargo.toml.bak"]
documentation = "https://onlook.com"

[lib]
crate-type = ["cdylib", "rlib"]

[dependencies]
onlook_react = { path = "./transform" }
serde_json = "1.0.79"
swc_cached = "0.3.18"
swc_common = { version = "0.33.12", features = ["concurrent"] }
swc_core = { version = "0.87.3", features = ["ecma_plugin_transform", "__common_plugin_transform"] }
swc_ecma_ast = "0.110.15"
swc_ecma_utils = "0.125.0"
swc_ecma_visit = "0.96.15"
swc_plugin = "0.90.0"
swc_plugin_macro = "0.9.15"
tracing = { version = "0.1.37", features = ["release_max_level_off"] }
