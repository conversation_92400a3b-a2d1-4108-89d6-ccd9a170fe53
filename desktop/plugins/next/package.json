{"name": "@onlook/nextjs", "version": "2.1.1", "description": "SWC plugin for onlook", "repository": {"type": "git", "url": "https://github.com/onlook-dev/desktop.git"}, "main": "plugin.wasm", "scripts": {"dev": "nodemon --watch transform --exec 'npm run build'", "cpy": "cp target/wasm32-wasi/release/swc_plugin_onlook.wasm ./plugin.wasm", "build": "cargo build --release --target wasm32-wasi && npm run cpy"}, "homepage": "https://onlook.com", "keywords": ["swc", "preprocessor", "onlook"], "preferUnplugged": true, "author": "onlook", "devDependencies": {"nodemon": "^3.1.4"}}