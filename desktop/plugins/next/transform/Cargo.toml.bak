[package]
authors = ["Ki<PERSON> Ho"]
description = "AST Transforms for import modularizer"
edition = "2021"
license = "Apache-2.0"
name = "onlook_react"
repository = "https://github.com/swc-project/plugins.git"
version = "0.22.0"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
serde = { version = "1", features = ["derive"] }
swc_atoms = "0.6.5"
swc_cached = "0.3.18"
swc_common = "0.33.12"
swc_core = { version = "0.87.3", features = ["__common"] }
swc_ecma_ast = "0.110.15"
swc_ecma_visit = "0.96.15" 
swc_plugin = "0.90.0"
swc_plugin_proxy = "0.39.17"

[dev-dependencies]
swc_ecma_parser = "0.141.33"
swc_ecma_transforms_base = "0.135.1"
swc_ecma_transforms_testing = "0.138.1"
testing = "0.35.14"
