[package]
authors         = ["Ki<PERSON> Ho"]
description     = "AST Transforms for import modularizer"
edition         = "2021"
license         = "Apache-2.0"
name            = "onlook"
repository      = "https://github.com/swc-project/plugins.git"
version         = "1.0.0"

[dependencies]
base64 = "0.22.1"
flate2 = "1.0.30"
serde               = { version = "1", features = ["derive"] }
serde_json          = "1.0.79"
swc_common          = "0.33.25"
swc_ecma_ast        = "0.113.0"
swc_ecma_visit      = "0.99.0"
swc_plugin_macro    = "0.9.16"

[dev-dependencies]
swc_ecma_transforms         = "0.230.0"
swc_ecma_transforms_testing = "0.141.0"
testing                     = "0.35.23"
