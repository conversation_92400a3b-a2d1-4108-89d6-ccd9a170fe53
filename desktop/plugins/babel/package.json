{"name": "@onlook/babel-plugin-react", "version": "2.1.1", "main": "build/bundle.cjs", "repository": {"type": "git", "url": "https://github.com/onlook-dev/desktop.git"}, "type": "module", "dependencies": {"@babel/core": "^7.23.6", "@babel/preset-env": "^7.23.6", "@babel/preset-react": "^7.23.3", "@babel/preset-typescript": "^7.24.1", "@babel/types": "^7.24.7", "fflate": "^0.8.2"}, "description": "React babel preprocessor for onlook", "directories": {"test": "tests"}, "scripts": {"dev": "rollup -c -w", "build": "rollup -c", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["react", "babel", "preprocessor", "onlook"], "author": "onlook", "license": "Apache-2.0", "homepage": "https://onlook.com", "devDependencies": {"@rollup/plugin-json": "^6.1.0", "@rollup/plugin-terser": "^0.4.4", "@rollup/plugin-typescript": "^11.1.6", "@types/bun": "latest", "rollup": "^4.9.6", "rollup-plugin-commonjs": "^10.1.0", "rollup-plugin-node-resolve": "^5.2.0", "typescript": "^5.0.0", "tslib": "^2.6.3"}}