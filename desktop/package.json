{"name": "@onlook/repo", "version": "0.0.0", "description": "Onlook Monorepo", "homepage": "https://onlook.com", "license": "Apache-2.0", "private": true, "author": {"name": "Onlook", "email": "<EMAIL>"}, "workspaces": ["packages/*", "apps/*", "tooling/*"], "scripts": {"build:foundation": "bun --filter @onlook/foundation build", "build:cli": "bun build:foundation && bun --filter onlook typecheck && bun --filter onlook build", "build:studio": "bun build:foundation && bun --filter @onlook/studio build", "clean": "git clean -xdf node_modules", "clean:workspaces": "bun --filter '*' clean", "db:gen": "bun --filter @onlook/supabase db:gen", "db:gen:local": "bun --filter @onlook/backend db:gen", "dev:foundation": "bun --filter @onlook/foundation dev", "dev:studio": "bun --filter @onlook/studio dev", "build": "bun --filter '*' build", "ci:build": "bun run build:foundation && bun --filter '*' build", "dev": "bun --elide-lines=0  --filter '*' dev", "test": "bun --filter '*' test", "format": "bun --filter '*' format", "lint": "bun --filter @onlook/studio lint", "typecheck": "bun --filter '*' typecheck", "increment_tag": "./scripts/increment_tag.sh", "publish_tag": "./scripts/publish_tag.sh", "remove_tag": "./scripts/remove_tag.sh", "download_bun": "bun run ./scripts/download_bun.ts", "prepare": "husky"}, "repository": {"type": "git", "url": "git+https://github.com/onlook-dev/desktop.git"}, "bugs": {"url": "https://github.com/onlook-dev/desktop/issues"}, "devDependencies": {"extract-zip": "^2.0.1", "husky": "^9.1.6"}}